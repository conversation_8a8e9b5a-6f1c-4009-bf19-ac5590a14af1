<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>aps-compensation</artifactId>
        <groupId>com.swcares.aps</groupId>
        <version>1.0.1_aps-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.swcares.aps</groupId>
    <artifactId>aps-compensation-model</artifactId>


    <dependencies>
        <!-- 声明基础框架技术栈stater -->
        <dependency>
            <groupId>com.swcares.baseframe</groupId>
            <artifactId>base-frame-starter</artifactId>
        </dependency>

        <!-- Security SSO -->
        <dependency>
            <groupId>com.swcares.components</groupId>
            <artifactId>system-oauth2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.components</groupId>
            <artifactId>system-encrypt</artifactId>
            <version>${swcares.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-workflow-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-user-center-model</artifactId>
            <version>1.0.1_aps-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-component-workflow</artifactId>
        </dependency>
    </dependencies>
</project>