package com.swcares.aps.compensation.model.baggage.accident.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("BAGGAGE_TRANSPORT_ACCIDENT_REL")
@ApiModel(value="BaggageTransportAccidentRel", description="行李运输单与事故单关联表")
public class BaggageTransportAccidentRelDO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    private Long tenantId;

    @ApiModelProperty(value = "运输单ID")
    private Long transportId;

    @ApiModelProperty(value = "关联备注")
    private String remark;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;
}