package com.swcares.aps.compensation.model.baggage.accident.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * ClassName：BaggageTransportReviewerSaveDTO <br>
 * Description：运输单审核人保存DTO <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2025/1/28 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "BaggageTransportReviewerSaveDTO对象", description = "运输单审核人保存DTO")
public class BaggageTransportReviewerSaveDTO {

    @ApiModelProperty(value = "运输单ID", required = true)
    @NotNull(message = "运输单ID不能为空")
    private Long transportId;

    @ApiModelProperty(value = "任务ID", required = true)
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    @ApiModelProperty(value = "审核人ID列表", required = true)
    @NotEmpty(message = "审核人ID列表不能为空")
    private Long[] auditorIds;
}